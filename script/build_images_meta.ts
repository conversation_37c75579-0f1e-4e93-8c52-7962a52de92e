import fs from "node:fs";
import path from "node:path";
import {
  getDirLastModified,
  getFileLastModified,
  isSupportedImage,
} from "./helpers";

const __dirname = new URL(".", import.meta.url).pathname;

const rootDir = path.join(__dirname, "../src/assets/puppeteer_images");

// Get all directories, excluding the meta.json file
const allDirs = fs
  .readdirSync(rootDir, { withFileTypes: true })
  .filter((entry) => entry.isDirectory())
  .map((entry) => entry.name);

console.log(`Found ${allDirs.length} directories to process:`, allDirs);

async function buildImagesMeta() {
  const meta = allDirs.map(async (dir) => {
    console.log(`\nProcessing directory: ${dir}`);

    const dirPath = path.join(rootDir, dir);
    const files = fs.readdirSync(dirPath);
    const dirLastModified = await getDirLastModified(dirPath);

    console.log(`Found ${files.length} files in ${dir}`);

    // Filter files to get only supported images
    const imageFiles: { path: string; lastModified: Date }[] = [];

    for (const file of files) {
      const filePath = path.join(dirPath, file);
      const isSupported = await isSupportedImage(filePath);
      const lastModified = await getFileLastModified(filePath);

      if (isSupported) {
        imageFiles.push({ path: path.join(dir, file), lastModified }); // Store relative path from puppeteer_images
      }
    }

    console.log(`Found ${imageFiles.length} supported images in ${dir}`);

    return {
      id: dir.replace(/[^a-zA-Z0-9]/g, "_"),
      folderName: dir,
      images: imageFiles.sort(), // Sort images for consistent ordering
      lastModified: dirLastModified,
    };
  });

  const metaResolved = await Promise.all(meta);

  // Filter out directories with no images
  const filteredMeta = metaResolved.filter((item) => item.images.length > 0);

  console.log(
    `\nGenerated metadata for ${filteredMeta.length} directories with images`
  );

  fs.writeFileSync(
    path.join(rootDir, "meta.json"),
    JSON.stringify(filteredMeta, null, 2)
  );

  console.log("✅ Meta data generation completed!");
}

// Run the function
buildImagesMeta().catch(console.error);
