import fs from "node:fs/promises";
import { fileType<PERSON>rom<PERSON>uffer } from "file-type";
import path from "node:path";

export async function checkFileIsImage(filePath): Promise<boolean> {
  try {
    const buffer = await fs.readFile(filePath);
    const type = await fileTypeFromBuffer(buffer);

    console.log(filePath, type?.mime);

    if (type && type.mime.startsWith("image/")) {
      return true;
    } else {
      return false;
    }
  } catch (error) {
    console.error(`Error checking file: ${error.message}`);
    return false;
  }
}

export const isSupportedImage = async (filePath: string) => {
  const isImage = await checkFileIsImage(filePath);

  if (!isImage) return false;

  const ext = filePath.split(".").pop();

  return ext === "png" || ext === "jpg" || ext === "jpeg" || ext === "webp";
};

export const getFileLastModified = async (filePath: string): Promise<Date> => {
  const stats = await fs.stat(filePath);
  return stats.mtime;
};

export const getDirLastModified = async (dirPath: string): Promise<Date> => {
  const files = await fs.readdir(dirPath);
  const fileTimes = await Promise.all(
    files.map((file) => getFileLastModified(path.join(dirPath, file)))
  );

  return new Date(Math.max(...fileTimes.map((time) => time.getTime())));
};
